import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:flutter/foundation.dart';
import 'ssl_fix_helper.dart';

/// OpenAI代理连接测试工具
/// 专门用于测试和修复OpenAI代理服务的连接问题
class OpenAIProxyTest {
  /// 测试OpenAI代理连接
  static Future<Map<String, dynamic>> testConnection({
    required String baseUrl,
    required String apiKey,
    String model = 'gpt-3.5-turbo',
    bool useTrustAllMode = false,
  }) async {
    try {
      print('开始测试OpenAI代理连接...');
      print('代理地址: $baseUrl');
      print('使用模型: $model');
      print('信任所有证书模式: $useTrustAllMode');

      // 创建HTTP客户端
      final client = useTrustAllMode 
          ? SSLFixHelper.createTrustAllClient(timeout: const Duration(seconds: 30))
          : SSLFixHelper.createTrustedServicesClient(timeout: const Duration(seconds: 30));

      // 构建请求URL
      final url = '$baseUrl/v1/chat/completions';
      print('请求URL: $url');

      // 构建请求体
      final requestBody = {
        'model': model,
        'messages': [
          {
            'role': 'user',
            'content': '请简单回答"你好"'
          }
        ],
        'max_tokens': 50,
        'temperature': 0.1,
      };

      print('请求体: ${jsonEncode(requestBody)}');

      // 发送请求
      final startTime = DateTime.now();
      final response = await client
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $apiKey',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(const Duration(seconds: 30));
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      client.close();

      print('响应状态码: ${response.statusCode}');
      print('响应时间: ${duration.inMilliseconds}ms');
      print('响应体: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final text = data['choices'][0]['message']['content'];
          return {
            'success': true,
            'message': 'OpenAI代理连接成功',
            'response': text,
            'duration': duration.inMilliseconds,
            'statusCode': response.statusCode,
          };
        } else {
          return {
            'success': false,
            'message': 'OpenAI代理响应格式异常',
            'response': response.body,
            'statusCode': response.statusCode,
          };
        }
      } else {
        return {
          'success': false,
          'message': 'OpenAI代理HTTP错误: ${response.statusCode}',
          'response': response.body,
          'statusCode': response.statusCode,
        };
      }
    } catch (e) {
      print('OpenAI代理测试失败: $e');
      
      // 分析错误类型并提供建议
      String errorMessage = e.toString();
      List<String> suggestions = [];
      
      if (errorMessage.contains('CERTIFICATE_VERIFY_FAILED') || 
          errorMessage.contains('HandshakeException')) {
        suggestions.addAll([
          '1. SSL证书验证失败，建议启用"信任所有证书"模式进行测试',
          '2. 检查代理服务器的SSL证书配置',
          '3. 确认系统时间是否正确',
          '4. 尝试更新系统根证书',
        ]);
      }
      
      if (errorMessage.contains('timeout') || errorMessage.contains('超时')) {
        suggestions.addAll([
          '1. 网络连接超时，请检查网络连接',
          '2. 代理服务器可能响应较慢，尝试增加超时时间',
          '3. 检查防火墙设置',
        ]);
      }
      
      if (errorMessage.contains('Connection refused') || 
          errorMessage.contains('No route to host')) {
        suggestions.addAll([
          '1. 无法连接到代理服务器，请检查地址是否正确',
          '2. 检查网络连接和DNS设置',
          '3. 确认代理服务器是否正常运行',
        ]);
      }

      return {
        'success': false,
        'message': 'OpenAI代理连接失败: $errorMessage',
        'error': errorMessage,
        'suggestions': suggestions,
        'canRetryWithTrustAll': errorMessage.contains('CERTIFICATE_VERIFY_FAILED'),
      };
    }
  }

  /// 测试多个常见的OpenAI代理服务
  static Future<Map<String, dynamic>> testMultipleProxies({
    required String apiKey,
    String model = 'gpt-3.5-turbo',
  }) async {
    final proxyUrls = [
      'https://api.openai-proxy.org',
      'https://api.openai-proxy.com',
      'https://openai.api2d.net',
      'https://api.chatanywhere.com.cn',
    ];

    final results = <String, Map<String, dynamic>>{};
    
    for (final proxyUrl in proxyUrls) {
      print('\n测试代理: $proxyUrl');
      
      // 先尝试正常模式
      var result = await testConnection(
        baseUrl: proxyUrl,
        apiKey: apiKey,
        model: model,
        useTrustAllMode: false,
      );
      
      // 如果SSL证书验证失败，尝试信任所有证书模式
      if (!result['success'] && result['canRetryWithTrustAll'] == true) {
        print('正常模式失败，尝试信任所有证书模式...');
        result = await testConnection(
          baseUrl: proxyUrl,
          apiKey: apiKey,
          model: model,
          useTrustAllMode: true,
        );
        result['usedTrustAllMode'] = true;
      }
      
      results[proxyUrl] = result;
    }

    // 统计结果
    final successCount = results.values.where((r) => r['success'] == true).length;
    final totalCount = results.length;

    return {
      'results': results,
      'summary': {
        'total': totalCount,
        'success': successCount,
        'failed': totalCount - successCount,
        'successRate': (successCount / totalCount * 100).toStringAsFixed(1),
      },
    };
  }

  /// 创建专门用于OpenAI代理的HTTP客户端
  static http.Client createOpenAIProxyClient({
    Duration? timeout,
    bool trustAllCertificates = false,
  }) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置超时
    httpClient.connectionTimeout = timeout ?? const Duration(seconds: 30);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 启用自动解压缩
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (OpenAI-Proxy-Client)';

    // 设置证书验证策略
    httpClient.badCertificateCallback = (cert, host, port) {
      if (trustAllCertificates) {
        print('OpenAI代理客户端 - 信任所有证书模式: $host');
        return true;
      }

      // 检查是否为OpenAI相关服务
      final openaiHosts = [
        'openai.com',
        'api.openai.com',
        'openai-proxy.org',
        'api.openai-proxy.org',
        'openai-proxy.com',
        'openaiproxy.org',
        'api2d.net',
        'chatanywhere.com.cn',
      ];

      for (final openaiHost in openaiHosts) {
        if (host.contains(openaiHost)) {
          print('OpenAI代理客户端 - 信任的OpenAI服务: $host');
          return true;
        }
      }

      print('OpenAI代理客户端 - 证书验证失败: $host:$port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      return false;
    };

    return IOClient(httpClient);
  }
}
